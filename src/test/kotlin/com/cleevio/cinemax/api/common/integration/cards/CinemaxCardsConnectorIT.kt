package com.cleevio.cinemax.api.common.integration.cards

import com.cleevio.cinemax.api.ConnectorTest
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBasketItemType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBasketType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBranch
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCountry
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCurrency
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsLanguage
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsPosType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsScreeningType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsState
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsUsageState
import com.cleevio.cinemax.api.common.integration.cards.dto.AvailableCardUsageProductResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.AvailableCardUsageTicketResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.CardUsageProductResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.CardUsageTicketResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.CountryLimitation
import com.cleevio.cinemax.api.common.integration.cards.dto.DiscountSubjectType
import com.cleevio.cinemax.api.common.integration.cards.dto.ListPurchasableSkusResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsProductBasketItem
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsRequest
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsTicketBasketItem
import com.cleevio.cinemax.api.common.util.fromJsonString
import com.cleevio.cinemax.api.common.util.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.test.context.TestPropertySource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Period

@TestPropertySource(
    properties = [
        "integration.cinemax-cards.base-url=http://localhost:8080",
        "integration.cinemax-cards.auth-base-url=http://localhost:8081"
    ]
)
class CinemaxCardsConnectorIT @Autowired constructor(
    private val underTest: CinemaxCardsConnector,
) : ConnectorTest() {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var authMockWebServer: MockWebServer

    @BeforeAll
    fun setUp() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8080)
        authMockWebServer = MockWebServer()
        authMockWebServer.start(8081)
    }

    @AfterAll
    fun tearDown() {
        mockWebServer.shutdown()
        authMockWebServer.shutdown()
    }

    @Test
    fun `test useCards, should serialize and deserialize correctly`() {
        mockWebServer.enqueue(
            MockResponse()
                .setBody(USE_CARDS_RESPONSE)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        val response = underTest.useCards(
            request = USE_CARDS_REQUEST.fromJsonString()
        )
        response.size shouldBe 2

        with(response[0] as CardUsageTicketResponse) {
            type shouldBe "CardUsageTicket"
            id shouldBe 1670
            state shouldBe CardsUsageState.CREATED
            cardCode shouldBe "CPZUBERC"
            timestamp shouldBe LocalDateTime.of(2025, 7, 10, 9, 39, 16, 0)
            pos shouldBe CardsPosType.CINEMA
            basketId shouldBe "88a45cb3-956a-46c9-a281-2103160aa6e0".toUUID()
            basketItemId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
            country shouldBe CardsCountry.SK
            branch shouldBe CardsBranch.BA
            originalPrice shouldBeEqualComparingTo 8.0.toBigDecimal()
            discountedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            discountAmount shouldBeEqualComparingTo 8.0.toBigDecimal()
            discountCurrency shouldBe CardsCurrency.EUR
            screeningDate shouldBe LocalDate.of(2025, 8, 1)
            screeningId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
            movieCode shouldBe "e0vvn3"
            screeningTypes shouldBe setOf(CardsScreeningType.ARTMAX_MOVIES)
            canBeCombined shouldBe false
            overridePosDistributorFee shouldBe mapOf(
                CardsCurrency.EUR to 1.0.toBigDecimal()
            )

            with(discountSubject) {
                id shouldBe 23
                type shouldBe DiscountSubjectType.TICKET
                relativeDiscount?.shouldBeEqualComparingTo(1.0.toBigDecimal())
                limitations shouldBe listOf()
                canBeCombined shouldBe false
                includeSurcharges shouldBe false
                distributorFee shouldBe mapOf(
                    CardsCurrency.EUR to 1.0.toBigDecimal()
                )

                auditMetadata?.let { metadata ->
                    with(metadata) {
                        createdAt shouldBe LocalDateTime.of(2025, 5, 19, 14, 37, 42)
                        updatedAt shouldBe LocalDateTime.of(2025, 5, 19, 14, 37, 42)
                        createdBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        createdBy?.name shouldBe "<EMAIL>"
                        createdBy?.email shouldBe "<EMAIL>"
                        updatedBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        updatedBy?.name shouldBe "<EMAIL>"
                        updatedBy?.email shouldBe "<EMAIL>"
                    }
                }
            }
        }

        with(response[1] as CardUsageProductResponse) {
            type shouldBe "CardUsageProduct"
            id shouldBe 1671
            state shouldBe CardsUsageState.CREATED
            cardCode shouldBe "CPZUBERC"
            pos shouldBe CardsPosType.CINEMA
            basketId shouldBe "88a45cb3-956a-46c9-a281-2103160aa6e0".toUUID()
            basketItemId shouldBe "508eba10-edcf-4f6e-9a8f-030d6b94ce8e".toUUID()
            country shouldBe CardsCountry.SK
            branch shouldBe CardsBranch.BA
            originalPrice shouldBeEqualComparingTo 6.0.toBigDecimal()
            discountedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            discountAmount shouldBeEqualComparingTo 6.0.toBigDecimal()
            discountCurrency shouldBe CardsCurrency.EUR
            productCode shouldBe "00035"
            canBeCombined shouldBe true

            with(discountSubject) {
                id shouldBe 7
                type shouldBe DiscountSubjectType.PRODUCT
                relativeDiscount?.shouldBeEqualComparingTo(1.0.toBigDecimal())
                limitations shouldBe listOf(
                    CountryLimitation(
                        countries = listOf(
                            CardsCountry.CZ,
                            CardsCountry.SK
                        )
                    )
                )
                canBeCombined shouldBe true
                includeSurcharges shouldBe true
                distributorFee shouldBe mapOf()

                auditMetadata?.let { metadata ->
                    with(metadata) {
                        createdAt shouldBe LocalDateTime.of(2025, 5, 16, 8, 12, 2)
                        updatedAt shouldBe LocalDateTime.of(2025, 5, 16, 8, 12, 2)
                        createdBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        createdBy?.name shouldBe "<EMAIL>"
                        createdBy?.email shouldBe "<EMAIL>"
                        updatedBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        updatedBy?.name shouldBe "<EMAIL>"
                        updatedBy?.email shouldBe "<EMAIL>"
                    }
                }
            }
        }

        val request = mockWebServer.takeRequest()
        request.method shouldBe HttpMethod.POST.name()
        request.path shouldBe "/card/useCards"
        request.headers[HttpHeaders.CONTENT_TYPE] shouldBe MediaType.APPLICATION_JSON_VALUE

        val requestBody: UseCardsRequest = request.body.readUtf8().fromJsonString()
        requestBody.cardCodes.size shouldBe 1
        requestBody.cardCodes shouldBe listOf("CPZUBERC")

        with(requestBody.payload) {
            basketId shouldBe "88a45cb3-956a-46c9-a281-2103160aa6e0".toUUID()
            type shouldBe CardsBasketType.BASKET
            pos shouldBe CardsPosType.CINEMA
            branch shouldBe CardsBranch.BA
            currency shouldBe CardsCurrency.EUR

            items.size shouldBe 2
            with(items[0] as UseCardsProductBasketItem) {
                basketItemId shouldBe "508eba10-edcf-4f6e-9a8f-030d6b94ce8e".toUUID()
                type shouldBe CardsBasketItemType.PRODUCT
                itemPrice shouldBeEqualComparingTo 6.0.toBigDecimal()
                quantity shouldBe 1
                productCode shouldBe "00035"
            }
            with(items[1] as UseCardsTicketBasketItem) {
                basketItemId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
                type shouldBe CardsBasketItemType.TICKET
                ticketBasePrice shouldBeEqualComparingTo 8.0.toBigDecimal()
                ticketTotalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
                screeningDate shouldBe LocalDate.of(2025, 8, 1)
                screeningId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
                movieCode shouldBe "e0vvn3"
                screeningTypes shouldBe setOf(CardsScreeningType.ARTMAX_MOVIES)
            }
        }
    }

    @Test
    fun `test listCardsUsages, should serialize and deserialize correctly`() {
        mockWebServer.enqueue(
            MockResponse()
                .setBody(LIST_CARDS_USAGES_RESPONSE)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        val response = underTest.listCardsUsages(
            request = LIST_CARDS_USAGES_REQUEST.fromJsonString()
        )
        response.size shouldBe 2

        with(response[0] as AvailableCardUsageTicketResponse) {
            basketItemId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
            discountSubjectId shouldBe 23
            cardTemplateName shouldBe "VIP karta"
            cardCode shouldBe "CPZUBERC"
            originalPrice shouldBeEqualComparingTo 8.0.toBigDecimal()
            discountedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            discountValue shouldBeEqualComparingTo 8.0.toBigDecimal()
            discountCurrency shouldBe CardsCurrency.EUR

            with(discountSubject) {
                id shouldBe 23
                type shouldBe DiscountSubjectType.TICKET
                relativeDiscount?.shouldBeEqualComparingTo(1.0.toBigDecimal())
                limitations shouldBe listOf()
                canBeCombined shouldBe false
                includeSurcharges shouldBe false
                distributorFee shouldBe mapOf(
                    CardsCurrency.EUR to 1.0.toBigDecimal()
                )

                auditMetadata?.let { metadata ->
                    with(metadata) {
                        createdAt shouldBe LocalDateTime.of(2025, 5, 19, 14, 37, 42)
                        updatedAt shouldBe LocalDateTime.of(2025, 5, 19, 14, 37, 42)
                        createdBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        createdBy?.name shouldBe "<EMAIL>"
                        createdBy?.email shouldBe "<EMAIL>"
                        updatedBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        updatedBy?.name shouldBe "<EMAIL>"
                        updatedBy?.email shouldBe "<EMAIL>"
                    }
                }
            }
        }

        with(response[1] as AvailableCardUsageProductResponse) {
            basketItemId shouldBe "508eba10-edcf-4f6e-9a8f-030d6b94ce8e".toUUID()
            discountSubjectId shouldBe 7
            cardTemplateName shouldBe "VIP karta"
            cardCode shouldBe "CPZUBERC"
            originalPrice shouldBeEqualComparingTo 6.0.toBigDecimal()
            originalItemPrice shouldBeEqualComparingTo 6.0.toBigDecimal()
            availableQuantity shouldBe 1
            discountedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            discountValue shouldBeEqualComparingTo 6.0.toBigDecimal()
            discountCurrency shouldBe CardsCurrency.EUR

            with(discountSubject) {
                id shouldBe 7
                type shouldBe DiscountSubjectType.PRODUCT
                relativeDiscount?.shouldBeEqualComparingTo(1.0.toBigDecimal())
                limitations shouldBe listOf(
                    CountryLimitation(
                        countries = listOf(
                            CardsCountry.CZ,
                            CardsCountry.SK
                        )
                    )
                )
                canBeCombined shouldBe true
                includeSurcharges shouldBe true
                distributorFee shouldBe mapOf()

                auditMetadata?.let { metadata ->
                    with(metadata) {
                        createdAt shouldBe LocalDateTime.of(2025, 5, 16, 8, 12, 2)
                        updatedAt shouldBe LocalDateTime.of(2025, 5, 16, 8, 12, 2)
                        createdBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        createdBy?.name shouldBe "<EMAIL>"
                        createdBy?.email shouldBe "<EMAIL>"
                        updatedBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        updatedBy?.name shouldBe "<EMAIL>"
                        updatedBy?.email shouldBe "<EMAIL>"
                    }
                }
            }
        }

        val request = mockWebServer.takeRequest()
        request.method shouldBe HttpMethod.POST.name()
        request.path shouldBe "/card/listAvailableCardsUsages"
        request.headers[HttpHeaders.CONTENT_TYPE] shouldBe MediaType.APPLICATION_JSON_VALUE

        val requestBody: UseCardsRequest = request.body.readUtf8().fromJsonString()
        requestBody.cardCodes.size shouldBe 1
        requestBody.cardCodes shouldBe listOf("CPZUBERC")

        with(requestBody.payload) {
            basketId shouldBe "88a45cb3-956a-46c9-a281-2103160aa6e0".toUUID()
            type shouldBe CardsBasketType.BASKET
            pos shouldBe CardsPosType.CINEMA
            branch shouldBe CardsBranch.BA
            currency shouldBe CardsCurrency.EUR

            items.size shouldBe 2
            with(items[0] as UseCardsProductBasketItem) {
                basketItemId shouldBe "508eba10-edcf-4f6e-9a8f-030d6b94ce8e".toUUID()
                type shouldBe CardsBasketItemType.PRODUCT
                itemPrice shouldBeEqualComparingTo 6.0.toBigDecimal()
                quantity shouldBe 1
                productCode shouldBe "00035"
            }
            with(items[1] as UseCardsTicketBasketItem) {
                basketItemId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
                type shouldBe CardsBasketItemType.TICKET
                ticketBasePrice shouldBeEqualComparingTo 8.0.toBigDecimal()
                ticketTotalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
                screeningDate shouldBe LocalDate.of(2025, 8, 1)
                screeningId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
                movieCode shouldBe "e0vvn3"
                screeningTypes shouldBe setOf(CardsScreeningType.ARTMAX_MOVIES)
            }
        }
    }

    @Test
    fun `test getCardUsage, should deserialize correctly`() {
        mockWebServer.enqueue(
            MockResponse()
                .setBody(GET_CARD_USAGE_RESPONSE)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        val response = underTest.getCardUsage(
            cardCode = "CPZUBERC",
            cardUsageId = 1670
        )

        with(response as CardUsageTicketResponse) {
            type shouldBe "CardUsageTicket"
            id shouldBe 1670
            state shouldBe CardsUsageState.CREATED
            cardCode shouldBe "CPZUBERC"
            timestamp shouldBe LocalDateTime.of(2025, 7, 10, 9, 39, 16, 0)
            pos shouldBe CardsPosType.CINEMA
            basketId shouldBe "88a45cb3-956a-46c9-a281-2103160aa6e0".toUUID()
            basketItemId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
            country shouldBe CardsCountry.SK
            branch shouldBe CardsBranch.BA
            originalPrice shouldBeEqualComparingTo 8.0.toBigDecimal()
            discountedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            discountedBasePrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            discountAmount shouldBeEqualComparingTo 8.0.toBigDecimal()
            discountCurrency shouldBe CardsCurrency.EUR
            screeningDate shouldBe LocalDate.of(2025, 8, 1)
            screeningId shouldBe "3fa85f64-5717-4562-b3fc-2c963f66afa6".toUUID()
            movieCode shouldBe "e0vvn3"
            screeningTypes shouldBe setOf(CardsScreeningType.ARTMAX_MOVIES)
            canBeCombined shouldBe false
            overridePosDistributorFee shouldBe mapOf(
                CardsCurrency.EUR to 1.0.toBigDecimal()
            )

            with(discountSubject) {
                id shouldBe 23
                type shouldBe DiscountSubjectType.TICKET
                relativeDiscount?.shouldBeEqualComparingTo(1.0.toBigDecimal())
                limitations shouldBe listOf()
                canBeCombined shouldBe false
                includeSurcharges shouldBe false
                distributorFee shouldBe mapOf()

                auditMetadata?.let { metadata ->
                    with(metadata) {
                        createdAt shouldBe LocalDateTime.of(2025, 5, 19, 14, 37, 42)
                        updatedAt shouldBe LocalDateTime.of(2025, 5, 19, 14, 37, 42)
                        createdBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        createdBy?.name shouldBe "<EMAIL>"
                        createdBy?.email shouldBe "<EMAIL>"
                        updatedBy?.id shouldBe "auth0|6765aaf4de196ffc61480e83"
                        updatedBy?.name shouldBe "<EMAIL>"
                        updatedBy?.email shouldBe "<EMAIL>"
                    }
                }
            }
        }

        val request = mockWebServer.takeRequest()
        request.method shouldBe HttpMethod.GET.name()
        request.path shouldBe "/card/CPZUBERC/usage/1670"
        request.headers[HttpHeaders.CONTENT_TYPE] shouldBe MediaType.APPLICATION_JSON_VALUE
    }

    @Test
    fun `test deleteCardUsage, should serialize correctly`() {
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(204)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        underTest.deleteCardUsage(
            cardCode = "CPZUBERC",
            cardUsageId = 1670
        )

        val request = mockWebServer.takeRequest()
        request.method shouldBe HttpMethod.DELETE.name()
        request.path shouldBe "/card/CPZUBERC/usage/1670"
        request.headers[HttpHeaders.CONTENT_TYPE] shouldBe MediaType.APPLICATION_JSON_VALUE
    }

    @Test
    fun `test getCard, should deserialize correctly`() {
        mockWebServer.enqueue(
            MockResponse()
                .setBody(GET_CARD_RESPONSE)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        val response = underTest.getCard(
            cardCode = "CO05CK6O"
        )

        with(response) {
            code shouldBe "CO05CK6O"
            templateId shouldBe 68
            templateNameInternal shouldBe "POS integration test 1"
            templateNameLocalized shouldBe mapOf(
                CardsLanguage.EN to "TSC - vstupenka 50%",
                CardsLanguage.SK to "TSC - vstupenka 50%",
                CardsLanguage.CS to "TSC - vstupenka 50%"
            )
            state shouldBe CardsState.ACTIVE
            activeFrom shouldBe LocalDateTime.of(2025, 7, 16, 11, 0, 31, 0)
            origin shouldBe "ADMIN"

            with(auditMetadata) {
                createdAt shouldBe LocalDateTime.of(2025, 7, 16, 8, 49, 49)
                updatedAt shouldBe LocalDateTime.of(2025, 7, 16, 11, 0, 31)
                createdBy?.id shouldBe "auth0|6873fea4627e70e222dd43b7"
                createdBy?.name shouldBe "Tomáš Schmidl"
                createdBy?.email shouldBe "<EMAIL>"
                updatedBy?.id shouldBe "auth0|6873fea4627e70e222dd43b7"
                updatedBy?.name shouldBe "Tomáš Schmidl"
                updatedBy?.email shouldBe "<EMAIL>"
            }
        }

        val request = mockWebServer.takeRequest()
        request.method shouldBe HttpMethod.GET.name()
        request.path shouldBe "/card/CO05CK6O"
        request.headers[HttpHeaders.CONTENT_TYPE] shouldBe MediaType.APPLICATION_JSON_VALUE
    }
}

private val USE_CARDS_REQUEST = """
{
    "cardIds": [
        "CPZUBERC"
    ],
    "payload": {
        "posBasketId": "88a45cb3-956a-46c9-a281-2103160aa6e0",
        "type": "basket",
        "pos": "CINEMA",
        "branch": "BA",
        "currency": "EUR",
        "items": [
            {
                "posBasketItemId": "508eba10-edcf-4f6e-9a8f-030d6b94ce8e",
                "type": "product",
                "itemPrice": 6,
                "quantity": 1,
                "posProductId": "00035"
            },
            {
                "posBasketItemId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "type": "ticket",
                "ticketBasePrice": 8,
                "ticketPriceWithSurcharges": 10,
                "screeningDate": "2025-08-01",
                "posScreeningId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "posMovieId": "e0vvn3",
                "screeningTypes": [
                    "ARTMAX_MOVIES"
                ]
            }
        ]
    }
}
""".trimIndent()

private val USE_CARDS_RESPONSE = """
[
    {
        "type": "CardUsageTicket",
        "id": 1670,
        "state": "CREATED",
        "cardId": "CPZUBERC",
        "timestamp": "2025-07-10T09:39:16.000000Z",
        "pos": "CINEMA",
        "posBasketId": "88a45cb3-956a-46c9-a281-2103160aa6e0",
        "posBasketItemId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "country": "SK",
        "branch": "BA",
        "discountSubject": {
            "id": 23,
            "type": "TICKET",
            "relativeDiscount": 1.0,
            "limitations": [],
            "canBeCombined": false,
            "includeSurcharges": false,
            "distributorFee": {
                "EUR": 1.0
            },
            "auditMetadata": {
                "createdAt": "2025-05-19T14:37:42.000000Z",
                "updatedAt": "2025-05-19T14:37:42.000000Z",
                "createdBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                },
                "updatedBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                }
            }
        },
        "originalPrice": 8.0,
        "discountedPrice": 0.0,
        "discountedBasePrice": 0.0,
        "discountAmount": 8.0,
        "discountCurrency": "EUR",
        "screeningDate": "2025-08-01",
        "posScreeningId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "posMovieId": "e0vvn3",
        "screeningTypes": [
            "ARTMAX_MOVIES"
        ],
        "canBeCombined": false,
        "overridePosDistributorFee": {
            "EUR": 1.0
        }
    },
    {
        "type": "CardUsageProduct",
        "id": 1671,
        "state": "CREATED",
        "cardId": "CPZUBERC",
        "timestamp": "2025-07-10T09:39:16.000000Z",
        "pos": "CINEMA",
        "posBasketId": "88a45cb3-956a-46c9-a281-2103160aa6e0",
        "posBasketItemId": "508eba10-edcf-4f6e-9a8f-030d6b94ce8e",
        "country": "SK",
        "branch": "BA",
        "discountSubject": {
            "id": 7,
            "type": "PRODUCT",
            "relativeDiscount": 1.0,
            "limitations": [
                {
                    "type": "Country",
                    "countries": [
                        "CZ",
                        "SK"
                    ]
                }
            ],
            "canBeCombined": true,
            "includeSurcharges": true,
            "distributorFee": {},
            "auditMetadata": {
                "createdAt": "2025-05-16T08:12:02.000000Z",
                "updatedAt": "2025-05-16T08:12:02.000000Z",
                "createdBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                },
                "updatedBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                }
            }
        },
        "originalPrice": 6.0,
        "discountedPrice": 0.0,
        "discountAmount": 6.0,
        "discountCurrency": "EUR",
        "posProductId": "00035",
        "canBeCombined": true
    }
]
""".trimIndent()

private val LIST_CARDS_USAGES_REQUEST = """
{
    "cardIds": [
        "CPZUBERC"
    ],
    "payload": {
        "posBasketId": "88a45cb3-956a-46c9-a281-2103160aa6e0",
        "type": "basket",
        "pos": "CINEMA",
        "branch": "BA",
        "currency": "EUR",
        "items": [
            {
                "posBasketItemId": "508eba10-edcf-4f6e-9a8f-030d6b94ce8e",
                "type": "product",
                "itemPrice": 6,
                "quantity": 1,
                "posProductId": "00035"
            },
            {
                "posBasketItemId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "type": "ticket",
                "ticketBasePrice": 8,
                "ticketPriceWithSurcharges": 10,
                "screeningDate": "2025-08-01",
                "posScreeningId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                "posMovieId": "e0vvn3",
                "screeningTypes": [
                    "ARTMAX_MOVIES"
                ]
            }
        ]
    }
}
""".trimIndent()

private val LIST_CARDS_USAGES_RESPONSE = """
[
    {
        "type": "ForTicket",
        "posBasketItemId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "discountSubjectId": 23,
        "cardTemplateName": "VIP karta",
        "cardId": "CPZUBERC",
        "originalPrice": 8.0,
        "originalBasePrice": 8.0,
        "discountedPrice": 0.0,
        "discountedBasePrice": 0.0,
        "discountValue": 8.0,
        "discountCurrency": "EUR",
        "discountSubject": {
            "id": 23,
            "type": "TICKET",
            "relativeDiscount": 1.0,
            "limitations": [],
            "canBeCombined": false,
            "includeSurcharges": false,
            "distributorFee": {
                "EUR": 1.0
            },
            "auditMetadata": {
                "createdAt": "2025-05-19T14:37:42.000000Z",
                "updatedAt": "2025-05-19T14:37:42.000000Z",
                "createdBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                },
                "updatedBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                }
            }
        }
    },
    {
        "type": "ForProduct",
        "posBasketItemId": "508eba10-edcf-4f6e-9a8f-030d6b94ce8e",
        "discountSubjectId": 7,
        "cardTemplateName": "VIP karta",
        "cardId": "CPZUBERC",
        "originalPrice": 6.0,
        "originalItemPrice": 6.0,
        "availableQuantity": 1,
        "discountedPrice": 0.0,
        "discountValue": 6.0,
        "discountCurrency": "EUR",
        "discountSubject": {
            "id": 7,
            "type": "PRODUCT",
            "relativeDiscount": 1.0,
            "limitations": [
                {
                    "type": "Country",
                    "countries": [
                        "CZ",
                        "SK"
                    ]
                }
            ],
            "canBeCombined": true,
            "includeSurcharges": true,
            "distributorFee": {},
            "auditMetadata": {
                "createdAt": "2025-05-16T08:12:02.000000Z",
                "updatedAt": "2025-05-16T08:12:02.000000Z",
                "createdBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                },
                "updatedBy": {
                    "id": "auth0|6765aaf4de196ffc61480e83",
                    "name": "<EMAIL>",
                    "email": "<EMAIL>"
                }
            }
        }
    }
]
""".trimIndent()

private val GET_CARD_USAGE_RESPONSE = """
{
    "type": "CardUsageTicket",
    "id": 1670,
    "state": "CREATED",
    "cardId": "CPZUBERC",
    "timestamp": "2025-07-10T09:39:16.000000Z",
    "pos": "CINEMA",
    "posBasketId": "88a45cb3-956a-46c9-a281-2103160aa6e0",
    "posBasketItemId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "country": "SK",
    "branch": "BA",
    "discountSubject": {
        "id": 23,
        "type": "TICKET",
        "relativeDiscount": 1.0,
        "limitations": [],
        "canBeCombined": false,
        "includeSurcharges": false,
        "distributorFee": {},
        "auditMetadata": {
            "createdAt": "2025-05-19T14:37:42.000000Z",
            "updatedAt": "2025-05-19T14:37:42.000000Z",
            "createdBy": {
                "id": "auth0|6765aaf4de196ffc61480e83",
                "name": "<EMAIL>",
                "email": "<EMAIL>"
            },
            "updatedBy": {
                "id": "auth0|6765aaf4de196ffc61480e83",
                "name": "<EMAIL>",
                "email": "<EMAIL>"
            }
        }
    },
    "originalPrice": 8.0,
    "discountedPrice": 0.0,
    "discountedBasePrice": 0.0,
    "discountAmount": 8.0,
    "discountCurrency": "EUR",
    "screeningDate": "2025-08-01",
    "posScreeningId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "posMovieId": "e0vvn3",
    "screeningTypes": [ 
        "ARTMAX_MOVIES"
    ],
    "canBeCombined": false,
    "overridePosDistributorFee": {
        "EUR": 1.0
    }
}
""".trimIndent()

private val GET_CARD_RESPONSE = """
{
    "id": "CO05CK6O",
    "templateId": 68,
    "templateNameInternal": "POS integration test 1",
    "templateNameLocalized": {
        "EN": "TSC - vstupenka 50%",
        "SK": "TSC - vstupenka 50%",
        "CS": "TSC - vstupenka 50%"
    },
    "state": "ACTIVE",
    "activeFrom": "2025-07-16T11:00:31.000000Z",
    "printCount": 1,
    "origin": "ADMIN",
    "statistics": {
        "usageCount": 0,
        "overallSavings": {}
    },
    "auditMetadata": {
        "createdAt": "2025-07-16T08:49:49.000000Z",
        "updatedAt": "2025-07-16T11:00:31.000000Z",
        "createdBy": {
            "id": "auth0|6873fea4627e70e222dd43b7",
            "name": "Tomáš Schmidl",
            "email": "<EMAIL>"
        },
        "updatedBy": {
            "id": "auth0|6873fea4627e70e222dd43b7",
            "name": "Tomáš Schmidl",
            "email": "<EMAIL>"
        }
    }
}
""".trimIndent()

    @Test
    fun `test listPurchasableSkus, should deserialize response correctly`() {
        // given
        authMockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(AUTH_RESPONSE)
        )

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(LIST_PURCHASABLE_SKUS_RESPONSE)
        )

        // when
        val response = underTest.listPurchasableSkus("TESTCARD123", "CINEMA")

        // then
        response.size shouldBe 2

        with(response[0]) {
            id shouldBe 34
            country shouldBe CardsCountry.SK
            currency shouldBe CardsCurrency.EUR
            price shouldBeEqualComparingTo 50.0.toBigDecimal()
            type shouldBe "CINEMA"
            instanceValidity shouldBe Period.of(2, 0, 0) // P2Y
        }

        with(response[1]) {
            id shouldBe 35
            country shouldBe CardsCountry.SK
            currency shouldBe CardsCurrency.EUR
            price shouldBeEqualComparingTo 30.0.toBigDecimal()
            type shouldBe "CINEMA"
            instanceValidity shouldBe Period.of(0, 6, 0) // P6M
        }
    }
}

private const val LIST_PURCHASABLE_SKUS_RESPONSE = """
[
    {
        "id": 34,
        "country": "SK",
        "currency": "EUR",
        "price": 50.0,
        "type": "CINEMA",
        "instanceValidity": "P2Y"
    },
    {
        "id": 35,
        "country": "SK",
        "currency": "EUR",
        "price": 30.0,
        "type": "CINEMA",
        "instanceValidity": "P6M"
    }
]
""".trimIndent()
