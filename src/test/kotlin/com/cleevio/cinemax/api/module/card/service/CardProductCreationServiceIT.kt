package com.cleevio.cinemax.api.module.card.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCountry
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCurrency
import com.cleevio.cinemax.api.common.integration.cards.dto.PurchasableSkuResponse
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcategory.service.command.CreateOrUpdateProductCategoryCommand
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Period

@IntegrationTest
@DatabaseCleanup
class CardProductCreationServiceIT @Autowired constructor(
    private val underTest: CardProductCreationService,
    private val productCategoryService: ProductCategoryService,
    private val productJpaFinderService: ProductJpaFinderService,
) {

    @Test
    fun `test createOrGetCardProducts, should create new card products`() {
        // given
        val cardProductCategory = productCategoryService.createOrUpdateProductCategory(
            CreateOrUpdateProductCategoryCommand(
                title = "Card Products",
                type = ProductCategoryType.CARD,
                taxRate = STANDARD_TAX_RATE,
                hexColorCode = "#FF0000"
            )
        )

        val skus = listOf(
            PurchasableSkuResponse(
                id = 34,
                country = CardsCountry.SK,
                currency = CardsCurrency.EUR,
                price = BigDecimal("50.0"),
                type = "CINEMA",
                instanceValidity = Period.of(2, 0, 0) // P2Y
            ),
            PurchasableSkuResponse(
                id = 35,
                country = CardsCountry.SK,
                currency = CardsCurrency.EUR,
                price = BigDecimal("30.0"),
                type = "CINEMA",
                instanceValidity = Period.of(0, 6, 0) // P6M
            )
        )

        // when
        val products = underTest.createOrGetCardProducts(skus,)

        // then
        products.size shouldBe 2

        with(products[0]) {
            originalId shouldBe 34
            code shouldBe "SK34"
            title shouldBe "VIP CINEMAX karta (2 years)"
            type shouldBe ProductType.PRODUCT
            price.isEqualTo(BigDecimal("50.0")) shouldBe true
            priceNoVat.isEqualTo(BigDecimal("40.65")) shouldBe true // 50 / 1.23
            active shouldBe true
            soldInBuffet shouldBe true
            soldInCafe shouldBe true
            soldInVip shouldBe true
            isPackagingDeposit shouldBe false
            productCategoryId shouldBe cardProductCategory.id
        }

        with(products[1]) {
            originalId shouldBe 35
            code shouldBe "SK35"
            title shouldBe "VIP CINEMAX karta (6 months)"
            type shouldBe ProductType.PRODUCT
            price.isEqualTo(BigDecimal("30.0")) shouldBe true
            priceNoVat.isEqualTo(BigDecimal("24.39")) shouldBe true // 30 / 1.23
        }
    }

    @Test
    fun `test createOrGetCardProducts, should return existing products if they already exist`() {
        // given
        val cardProductCategory = productCategoryService.createOrUpdateProductCategory(
            CreateOrUpdateProductCategoryCommand(
                title = "Card Products",
                type = ProductCategoryType.CARD,
                taxRate = STANDARD_TAX_RATE,
                hexColorCode = "#FF0000"
            )
        )

        val sku = PurchasableSkuResponse(
            id = 36,
            country = CardsCountry.SK,
            currency = CardsCurrency.EUR,
            price = BigDecimal("25.0"),
            type = "CINEMA",
            instanceValidity = Period.of(1, 0, 0) // P1Y
        )

        // Create product first time
        val firstCallProducts = underTest.createOrGetCardProducts(listOf(sku),)
        val firstProduct = firstCallProducts[0]

        // when - call again with same SKU
        val secondCallProducts = underTest.createOrGetCardProducts(listOf(sku),)
        val secondProduct = secondCallProducts[0]

        // then - should return the same product instance
        firstProduct.id shouldBe secondProduct.id
        firstProduct.code shouldBe secondProduct.code
        firstProduct.title shouldBe secondProduct.title
    }
}
