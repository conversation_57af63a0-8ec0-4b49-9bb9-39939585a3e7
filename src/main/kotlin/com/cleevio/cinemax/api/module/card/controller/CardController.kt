package com.cleevio.cinemax.api.module.card.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.module.card.controller.dto.CardProductResponse
import com.cleevio.cinemax.api.module.card.controller.mapper.CardProductResponseMapper
import com.cleevio.cinemax.api.module.card.service.CardPurchaseService
import com.cleevio.cinemax.api.module.card.service.command.ListPurchasableCardProductsCommand
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "POS Cards")
@RestController
@RequestMapping("/pos-app/cards")
class CardController(
    private val cardPurchaseService: CardPurchaseService,
    private val cardProductResponseMapper: CardProductResponseMapper,
) {

    @Operation(
        summary = "List purchasable card products",
        description = "Lists all available card products for purchase after scanning a physical card"
    )
    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/{cardId}/purchasable-products", produces = [ApiVersion.VERSION_1_JSON])
    fun listPurchasableCardProducts(
        @PathVariable cardId: UUID
    ): List<CardProductResponse> = cardPurchaseService
        .listPurchasableCardProducts(
            ListPurchasableCardProductsCommand(cardId = cardId)
        )
        .let { products -> cardProductResponseMapper.mapList(products) }
}
