package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.Language
import com.cleevio.cinemax.api.common.constant.TaxRateType
import com.cleevio.cinemax.api.common.integration.cards.dto.PurchasableSkuResponse
import com.cleevio.cinemax.api.common.util.subtractTax
import com.cleevio.cinemax.api.common.util.toHumanReadableFormat
import com.cleevio.cinemax.api.module.card.service.CardJpaFinderService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.model.CardProductModel
import com.cleevio.cinemax.api.module.product.service.model.toModel
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CardProductCreationService(
    private val cardJpaFinderService: CardJpaFinderService,
    private val productRepository: ProductRepository,
    private val productJpaFinderService: ProductJpaFinderService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    private val productCategoryJpaFinderService: ProductCategoryJpaFinderService,
) {

    @Transactional
    fun createOrGetCardProducts(
        cardId: UUID,
        purchasableCardProducts: List<PurchasableSkuResponse>,
    ): List<CardProductModel> {
        val language = Language.SLOVAK // TODO

        val card = cardJpaFinderService.getById(cardId)

        val cardProductCategory = productCategoryJpaFinderService.getByType(ProductCategoryType.CARD)
        val standardTaxRate = cinemaxConfigProperties.getTaxRate(TaxRateType.STANDARD)

        val products = purchasableCardProducts.map { it ->
            val productCode = "${it.country}${it.id}"

            productJpaFinderService
                .findNonDeletedByCode(productCode)
                ?: run {
                    val product = Product(
                        originalId = it.id.toInt(),
                        code = productCode,
                        originalCode = null,
                        productCategoryId = cardProductCategory.id,
                        title = "${card.title} (${it.instanceValidity.toHumanReadableFormat(language)})",
                        type = ProductType.PRODUCT,
                        price = it.price,
                        flagshipPrice = null,
                        priceNoVat = it.price.subtractTax(standardTaxRate),
                        active = true,
                        order = null,
                        soldInBuffet = true,
                        soldInCafe = true,
                        soldInVip = true,
                        isPackagingDeposit = false
                    )

                    productRepository.save(product)
                }
        }

        return products.map { it.toModel() }
    }
}
