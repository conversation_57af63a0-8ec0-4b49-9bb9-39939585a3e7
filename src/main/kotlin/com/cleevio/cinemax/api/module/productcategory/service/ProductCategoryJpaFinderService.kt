package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.productcategory.exception.ProductCategoryNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class ProductCategoryJpaFinderService(
    private val repository: ProductCategoryRepository,
) {

    fun findNonDeletedById(id: UUID): ProductCategory? = repository.findByIdAndDeletedAtIsNull(id)

    fun getNonDeletedById(id: UUID): ProductCategory = findNonDeletedById(id) ?: throw ProductCategoryNotFoundException()

    fun findAllNonDeletedByIdIn(ids: Set<UUID>): List<ProductCategory> = repository.findAllByIdInAndDeletedAtIsNull(ids)

    fun findNonDeletedByCode(code: String): ProductCategory? =
        repository.findByCodeAndDeletedAtIsNull(code)

    fun getNonDeletedByCode(originalCode: String): ProductCategory =
        findNonDeletedByCode(originalCode) ?: throw ProductCategoryNotFoundException()

    fun getByType(type: ProductCategoryType): ProductCategory = repository.findByTypeAndDeletedAtIsNull(type)
        ?: throw ProductCategoryNotFoundException()
}
