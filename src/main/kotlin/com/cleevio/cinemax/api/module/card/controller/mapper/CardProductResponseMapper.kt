package com.cleevio.cinemax.api.module.card.controller.mapper

import com.cleevio.cinemax.api.module.card.controller.dto.CardProductResponse
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.model.CardProductModel
import org.springframework.stereotype.Component

@Component
class CardProductResponseMapper {

    fun mapSingle(input: CardProductModel) = CardProductResponse(
        productId = input.productId,
        title = input.title,
        price = input.price,
    )

    fun mapList(input: List<CardProductModel>) = input.map { mapSingle(it) }
}
