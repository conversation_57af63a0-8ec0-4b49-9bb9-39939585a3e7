package com.cleevio.cinemax.api.common.integration.cards.dto

import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCurrency
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCountry
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Period

typealias ListPurchasableSkusResponse = List<PurchasableSkuResponse>

data class PurchasableSkuResponse(
    val id: Long,
    val country: CardsCountry,
    val currency: CardsCurrency,
    val price: BigDecimal,
    val type: String,
    @get:JsonProperty("instanceValidity")
    val instanceValidityString: String
) {
    /**
     * Converts the instanceValidity string (e.g., "P2Y", "P6M") to a Period object.
     */
    val instanceValidity: Period
        get() = Period.parse(instanceValidityString)
}


