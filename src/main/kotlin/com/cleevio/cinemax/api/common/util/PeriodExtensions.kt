package com.cleevio.cinemax.api.common.util

import com.cleevio.cinemax.api.common.constant.Language
import java.time.Period

/**
 * Converts a Java Period to a human-readable format for card titles.
 * Examples:
 * - P30D -> "30 dní" (SK), "30 dní" (CZ), "30 zile" (RO)
 * - P1M -> "1 mesiac" (SK), "1 měsíc" (CZ), "1 lună" (RO)
 * - P3M -> "3 mesiace" (SK), "3 měsíce" (CZ), "3 luni" (RO)
 * - P6M -> "6 mesiacov" (SK), "6 měsíců" (CZ), "6 luni" (RO)
 * - P1Y -> "1 rok" (SK), "1 rok" (CZ), "1 an" (RO)
 */
fun Period.toHumanReadableFormat(language: Language): String {
    val parts = mutableListOf<String>()

    if (years > 0) {
        parts.add(formatYears(years, language))
    }

    if (months > 0) {
        parts.add(formatMonths(months, language))
    }

    if (days > 0) {
        parts.add(formatDays(days, language))
    }

    return when {
        parts.isEmpty() -> formatZeroDays(language)
        parts.size == 1 -> parts[0]
        parts.size == 2 -> "${parts[0]} ${parts[1]}"
        else -> parts.dropLast(1).joinToString(", ") + " ${getAndWord(language)} " + parts.last()
    }
}

private fun formatYears(years: Int, language: Language): String {
    return when (language) {
        Language.SLOVAK -> when {
            years == 1 -> "1 rok"
            years in 2..4 -> "$years roky"
            else -> "$years rokov"
        }
        Language.CZECH -> when {
            years == 1 -> "1 rok"
            years in 2..4 -> "$years roky"
            else -> "$years let"
        }
        Language.ROMANIAN -> when {
            years == 1 -> "1 an"
            else -> "$years ani"
        }
    }
}

private fun formatMonths(months: Int, language: Language): String {
    return when (language) {
        Language.SLOVAK -> when {
            months == 1 -> "1 mesiac"
            months in 2..4 -> "$months mesiace"
            else -> "$months mesiacov"
        }
        Language.CZECH -> when {
            months == 1 -> "1 měsíc"
            months in 2..4 -> "$months měsíce"
            else -> "$months měsíců"
        }
        Language.ROMANIAN -> when {
            months == 1 -> "1 lună"
            else -> "$months luni"
        }
    }
}

private fun formatDays(days: Int, language: Language): String {
    return when (language) {
        Language.SLOVAK -> when {
            days == 1 -> "1 deň"
            else -> "$days dní"
        }
        Language.CZECH -> when {
            days == 1 -> "1 den"
            days in 2..4 -> "$days dny"
            else -> "$days dní"
        }
        Language.ROMANIAN -> when {
            days == 1 -> "1 zi"
            else -> "$days zile"
        }
    }
}

private fun formatZeroDays(language: Language): String {
    return when (language) {
        Language.SLOVAK -> "0 dní"
        Language.CZECH -> "0 dní"
        Language.ROMANIAN -> "0 zile"
    }
}

private fun getAndWord(language: Language): String {
    return when (language) {
        Language.SLOVAK -> "a"
        Language.CZECH -> "a"
        Language.ROMANIAN -> "și"
    }
}
